import React, { useState, useEffect } from 'react';
import { Plus, Globe, CheckCircle, XCircle, Clock, AlertTriangle, Trash2, RefreshCw, Copy, ExternalLink, Settings, Zap, RotateCcw } from 'lucide-react';
import { But<PERSON> } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { Alert, AlertDescription } from '../ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { Separator } from '../ui/separator';
import { toast } from 'sonner';
import { useAuthStore } from '@/stores/authStore';

interface CustomDomain {
  id: string;
  domain: string;
  status: 'pending' | 'active' | 'failed' | 'expired';
  ssl_status: 'pending' | 'active' | 'failed';
  verification_method: 'dns' | 'http';
  verification_token?: string;
  dns_records?: {
    cname: { name: string; value: string; type: string };
    txt: { name: string; value: string; type: string };
  };
  cloudflare_zone_id?: string;
  cloudflare_custom_hostname_id?: string;
  pages_project_name?: string;
  verified: boolean;
  last_verified_at?: string;
  error_message?: string;
  created_at: string;
  updated_at: string;
  cloudflare_status?: {
    zone_exists: boolean;
    dns_records: any[];
  };
}

const CustomDomainsManager: React.FC = () => {
  const [domains, setDomains] = useState<CustomDomain[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [newDomain, setNewDomain] = useState('');
  const [projectName, setProjectName] = useState('qr-redirect-backend-v2');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  // get user session
  const { session, status, init, signIn } = useAuthStore();
  
  useEffect(() => {
    if(session){
      fetchDomains();
    }
  }, [session]);

   // Show login UI when user is not authenticated
   if ( status === 'unauthenticated') {
    return (
      <div className="min-h-[60vh] flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          {/* Login illustration */}
          <div className="w-24 h-24 mx-auto mb-6 bg-blue-100 rounded-full flex items-center justify-center">
            <svg className="w-12 h-12 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>

          <h2 className="text-2xl font-bold text-gray-900 mb-3">Welcome to QRAnalytica</h2>
          <p className="text-gray-600 mb-6 leading-relaxed">
            Sign in to access your custom domains.
          </p>

          <div className="flex flex-col gap-3 justify-center">
            <button
              onClick={signIn}
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg font-medium hover:from-blue-700 hover:to-blue-800 transition-all duration-200 transform hover:scale-105 shadow-lg flex items-center justify-center gap-2"
            >
              <svg className="w-5 h-5" viewBox="0 0 24 24">
                <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              Sign in with Google
            </button>
          </div>
        </div>
      </div>
    );
  }

  const fetchDomains = async () => {
    try {
      setLoading(true);
      // Use fallback user ID if session is not available
      const userId = session?.user?.id || 'default-user';
      const response = await fetch(`/api/custom-domains?user_id=${userId}`);
      const data = await response.json();

      if (data.success) {
        setDomains(data.domains);
      } else {
        toast.error('Failed to fetch domains');
      }
    } catch (error) {
      console.error('Error fetching domains:', error);
      toast.error('Failed to fetch domains');
    } finally {
      setLoading(false);
    }
  };

  const addDomain = async () => {
    if (!newDomain.trim()) {
      toast.error('Please enter a domain name');
      return;
    }

    try {
      setIsSubmitting(true);
      const response = await fetch('/api/custom-domains', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          domain: newDomain.trim(),
          pages_project_name: projectName.trim() || undefined,
          user_id: session?.user?.id || 'default-user'
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success('Domain added successfully');
        setDomains([data.domain, ...domains]);
        setNewDomain('');
        setProjectName('');
        setIsAddDialogOpen(false);
      } else {
        toast.error(data.error || 'Failed to add domain');
      }
    } catch (error) {
      console.error('Error adding domain:', error);
      toast.error('Failed to add domain');
    } finally {
      setIsSubmitting(false);
    }
  };
 

  const deleteDomain = async (domainId: string) => {
    if (!confirm('Are you sure you want to delete this domain?')) {
      return;
    }

    // Use fallback user ID if session is not available
    const userId = session?.user?.id || 'default-user';

    try {
      const response = await fetch(`/api/custom-domains?id=${domainId}&user_id=${userId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Domain deleted successfully');
        setDomains(domains.filter(d => d.id !== domainId));
      } else {
        toast.error(data.error || 'Failed to delete domain');
      }
    } catch (error) {
      console.error('Error deleting domain:', error);
      toast.error('Failed to delete domain');
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard');
  };

  const syncDomainStatus = async (domainId?: string) => {
    try {
      setIsSyncing(true);
      const userId = session?.user?.id || 'default-user';

      const response = await fetch('/api/custom-domains/sync-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          domain_id: domainId,
          user_id: userId
        }),
      });

      const data = await response.json();

      if ((data as any).success) {
        toast.success((data as any).message || 'Domain status synced successfully');
        fetchDomains(); // Refresh the list
      } else {
        toast.error((data as any).error || 'Failed to sync domain status');
      }
    } catch (error) {
      console.error('Error syncing domain status:', error);
      toast.error('Failed to sync domain status');
    } finally {
      setIsSyncing(false);
    }
  };

  

  const getStatusBadge = (status: string, sslStatus?: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800 border-green-200"><CheckCircle className="w-3 h-3 mr-1" />Active</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200"><Clock className="w-3 h-3 mr-1" />Pending</Badge>;
      case 'failed':
        return <Badge className="bg-red-100 text-red-800 border-red-200"><XCircle className="w-3 h-3 mr-1" />Failed</Badge>;
      case 'expired':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200"><AlertTriangle className="w-3 h-3 mr-1" />Expired</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Custom Domains</h1>
            <p className="text-muted-foreground">Manage custom domains for your QRAnalytica</p>
          </div>
        </div>
        <div className="grid gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Custom Domains</h1>
          <p className="text-muted-foreground">Manage custom domains for your QRAnalytica</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => syncDomainStatus()}
            disabled={isSyncing}
          >
            <RotateCcw className={isSyncing ? "w-4 h-4 mr-2 animate-spin" : "w-4 h-4 mr-2"} />
            {isSyncing ? 'Syncing...' : 'Sync Status'}
          </Button>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">
                <Plus className="w-4 h-4 mr-2" />
                Add Domain
              </Button>
            </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Add Custom Domain</DialogTitle>
              <DialogDescription>
                Add a custom domain to point to your QRAnalytica.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="domain">Domain Name</Label>
                <Input
                  id="domain"
                  placeholder="example.com"
                  value={newDomain}
                  onChange={(e) => setNewDomain(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="project">Pages Project Name (Optional)</Label>
                <Input
                  id="project"
                  placeholder="my-project"
                  value={projectName}
                  onChange={(e) => setProjectName(e.target.value)}
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={addDomain} disabled={isSubmitting}>
                  {isSubmitting ? 'Adding...' : 'Add Domain'}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
        </div>
      </div>

      {/* Domains List */}
      {domains.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Globe className="w-12 h-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No custom domains</h3>
            <p className="text-gray-600 text-center mb-4">
              Get started by adding your first custom domain to your QRAnalytica.
            </p>
            <Button onClick={() => setIsAddDialogOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Add Your First Domain
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {domains.map((domain) => (
            <Card key={domain.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Globe className="w-5 h-5 text-blue-600" />
                    <div>
                      <CardTitle className="text-lg">{domain.domain}</CardTitle>
                      {/* <CardDescription>
                        {domain.pages_project_name ? `Project: ${domain.pages_project_name}` : ''}
                      </CardDescription> */}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusBadge(domain.status)}
                    {/* <Button
                      variant="outline"
                      size="sm"
                      onClick={() => verifyDomain(domain.id)}
                      disabled={domain.status === 'active'}
                    >
                      <RefreshCw className="w-3 h-3 mr-1" />
                      Verify
                    </Button> */}
                    {/* {domain.status === 'active' && !domain.cloudflare_zone_id && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setupCloudflare(domain.id)}
                        className="text-blue-600 hover:text-blue-700"
                      >
                        <Zap className="w-3 h-3 mr-1" />
                        Setup CF
                      </Button>
                    )}
                    {domain.cloudflare_zone_id && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeFromCloudflare(domain.id)}
                        className="text-orange-600 hover:text-orange-700"
                      >
                        <Settings className="w-3 h-3 mr-1" />
                        Remove CF
                      </Button>
                    )} */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => deleteDomain(domain.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {domain.status === 'pending' && domain.dns_records && (
                  <Alert className="mb-4">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      Please add the following DNS records to verify your domain:
                    </AlertDescription>
                  </Alert>
                )}
                
                {domain.dns_records  && (
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">Required DNS Records:</h4>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Type</TableHead>
                            <TableHead>Name</TableHead>
                            <TableHead>Value</TableHead>
                            <TableHead>Action</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          <TableRow>
                            <TableCell>{domain.dns_records.cname.type}</TableCell>
                            <TableCell className="font-mono text-sm">{domain.dns_records.cname.name}</TableCell>
                            <TableCell className="font-mono text-sm">{domain.dns_records.cname.value}</TableCell>
                            <TableCell>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => copyToClipboard(domain.dns_records!.cname.value)}
                              >
                                <Copy className="w-3 h-3" />
                              </Button>
                            </TableCell>
                          </TableRow>
                     
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                )}

                {domain.cloudflare_zone_id && (
                  <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center space-x-2 mb-2">
                      <Zap className="w-4 h-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-800">Cloudflare Integration</span>
                    </div>
                    <div className="text-xs text-blue-700">
                      <p>✓ Domain configured with QRAnalytica</p>
                      <p>✓ DNS records managed automatically</p>
                      <p>✓ SSL certificate provisioned</p>
                    </div>
                  </div>
                )}

                {domain.error_message && (
                  <Alert className="mt-4 border-red-200 bg-red-50">
                    <XCircle className="h-4 w-4 text-red-600" />
                    <AlertDescription className="text-red-800">
                      {domain.error_message}
                    </AlertDescription>
                  </Alert>
                )}

                <div className="flex items-center justify-between mt-4 pt-4 border-t">
                  <div className="text-sm text-gray-600">
                    Added {new Date(domain.created_at).toLocaleDateString()}
                    {domain.last_verified_at && (
                      <span className="ml-2">
                        • Last verified {new Date(domain.last_verified_at).toLocaleDateString()}
                      </span>
                    )}
                  </div>
                  {domain.status === 'active' && (
                    <Button variant="outline" size="sm" asChild>
                      <a href={"https://" + domain.domain} target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="w-3 h-3 mr-1" />
                        Visit
                      </a>
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default CustomDomainsManager;
